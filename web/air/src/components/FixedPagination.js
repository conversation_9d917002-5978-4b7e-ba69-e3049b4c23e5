import React, { useEffect } from 'react';
import { Pagination } from '@douyinfe/semi-ui';
import './FixedPagination.css';

const FixedPagination = ({
  currentPage,
  pageSize,
  total,
  onPageChange,
  onPageSizeChange,
  pageSizeOpts = [10, 20, 50, 100],
  showSizeChanger = true,
  ...props
}) => {
  // Don't render if there's only one page or no data
  const totalPages = Math.ceil(total / pageSize);

  useEffect(() => {
    console.log(`[Air FixedPagination] Component rendered - currentPage: ${currentPage}, pageSize: ${pageSize}, total: ${total}, totalPages: ${totalPages}`);
  }, [currentPage, pageSize, total, totalPages]);

  if (!total || totalPages <= 1) {
    console.log(`[Air FixedPagination] Hiding pagination - total: ${total}, totalPages: ${totalPages}`);
    return null;
  }

  const handlePageChange = (page, pageSize) => {
    console.log(`[Air FixedPagination] Page change - from ${currentPage} to ${page}, pageSize: ${pageSize}`);
    if (onPageChange) {
      onPageChange(page, pageSize);
    }
  };

  const handlePageSizeChange = (currentPage, pageSize) => {
    console.log(`[Air FixedPagination] Page size change - currentPage: ${currentPage}, new pageSize: ${pageSize}`);
    if (onPageSizeChange) {
      onPageSizeChange(currentPage, pageSize);
    }
  };

  console.log(`[Air FixedPagination] Rendering pagination`);
  return (
    <div className="fixed-pagination-container">
      <Pagination
        currentPage={currentPage}
        pageSize={pageSize}
        total={total}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        pageSizeOpts={pageSizeOpts}
        showSizeChanger={showSizeChanger}
        showQuickJumper={false}
        size="small"
        {...props}
      />
    </div>
  );
};

export default FixedPagination;
