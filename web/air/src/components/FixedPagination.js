import React from 'react';
import { Pagination } from '@douyinfe/semi-ui';
import './FixedPagination.css';

const FixedPagination = ({
  currentPage,
  pageSize,
  total,
  onPageChange,
  onPageSizeChange,
  pageSizeOpts = [10, 20, 50, 100],
  showSizeChanger = true,
  ...props
}) => {
  // Don't render if there's only one page or no data
  const totalPages = Math.ceil(total / pageSize);
  if (!total || totalPages <= 1) {
    return null;
  }

  return (
    <div className="fixed-pagination-container">
      <Pagination
        currentPage={currentPage}
        pageSize={pageSize}
        total={total}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        pageSizeOpts={pageSizeOpts}
        showSizeChanger={showSizeChanger}
        showQuickJumper={false}
        size="small"
        {...props}
      />
    </div>
  );
};

export default FixedPagination;
