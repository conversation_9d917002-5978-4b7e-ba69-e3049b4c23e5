.fixed-pagination-container {
  position: fixed !important;
  bottom: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 1000 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  /* Ensure visibility and prevent page jumping */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.fixed-pagination-container .semi-pagination {
  margin: 0;
}

.fixed-pagination-container .semi-pagination-item {
  margin: 0 2px;
  border-radius: 4px;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.fixed-pagination-container .semi-pagination-item:hover {
  background: #f8f9fa;
}

.fixed-pagination-container .semi-pagination-item-active {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.fixed-pagination-container .semi-pagination-item-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .fixed-pagination-container {
    bottom: 10px !important;
    left: 10px !important;
    right: 10px !important;
    transform: none !important;
    width: auto !important;
    max-width: calc(100vw - 20px) !important;
    padding: 8px 12px !important;
    /* Ensure visibility on mobile */
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .fixed-pagination-container .semi-pagination-item {
    min-width: 28px !important;
    height: 28px !important;
    font-size: 12px !important;
    margin: 0 1px !important;
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .fixed-pagination-container .semi-pagination-page-size-changer {
    margin-left: 8px !important;
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

/* Force show pagination items on mobile - override Semi UI responsive hiding */
@media (max-width: 767px) {
  .fixed-pagination-container .semi-pagination-item {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  /* Ensure ellipsis and page numbers are visible */
  .fixed-pagination-container .semi-pagination-item-disabled {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .fixed-pagination-container .semi-pagination-page-size-changer {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  /* Ensure the entire pagination component is visible */
  .fixed-pagination-container .semi-pagination {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

/* Prevent page jumping by ensuring pagination doesn't affect document flow */
body {
  scroll-behavior: smooth;
}

/* Ensure pagination buttons don't cause scroll jumps */
.fixed-pagination-container * {
  scroll-behavior: auto !important;
}

.fixed-pagination-container button,
.fixed-pagination-container .semi-button {
  scroll-behavior: auto !important;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .fixed-pagination-container {
    background: rgba(45, 45, 45, 0.95) !important;
    border-color: #444 !important;
    color: white !important;
  }
}
