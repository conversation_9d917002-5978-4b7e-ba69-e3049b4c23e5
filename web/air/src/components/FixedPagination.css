.fixed-pagination-container {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.fixed-pagination-container .semi-pagination {
  margin: 0;
}

.fixed-pagination-container .semi-pagination-item {
  margin: 0 2px;
  border-radius: 4px;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.fixed-pagination-container .semi-pagination-item:hover {
  background: #f8f9fa;
}

.fixed-pagination-container .semi-pagination-item-active {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.fixed-pagination-container .semi-pagination-item-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .fixed-pagination-container {
    bottom: 10px;
    left: 10px;
    right: 10px;
    transform: none;
    width: auto;
    max-width: calc(100vw - 20px);
    padding: 8px 12px;
  }
  
  .fixed-pagination-container .semi-pagination-item {
    min-width: 28px;
    height: 28px;
    font-size: 12px;
    margin: 0 1px;
  }
  
  .fixed-pagination-container .semi-pagination-page-size-changer {
    margin-left: 8px;
  }
}

/* Force show pagination items on mobile - override Semi UI responsive hiding */
@media (max-width: 767px) {
  .fixed-pagination-container .semi-pagination-item {
    display: flex !important;
  }
  
  /* Ensure ellipsis and page numbers are visible */
  .fixed-pagination-container .semi-pagination-item-disabled {
    display: flex !important;
  }
  
  .fixed-pagination-container .semi-pagination-page-size-changer {
    display: flex !important;
  }
}
