/* ==============================|| MOBILE OVERRIDE FRAMEWORK - AIR TEMPLATE ||============================== */
/* MAXIMUM SPECIFICITY CSS TO OVERRIDE ALL EXISTING STYLES */
/* This file uses the highest possible CSS specificity to ensure mobile styles are applied */

@media screen and (max-width: 768px) {

  /* ==============================|| CRITICAL LAYOUT RESET ||============================== */

  /* Maintain proper body spacing for mobile */
  html body,
  body {
    padding-top: 60px !important;
    /* Keep header space */
    margin-top: 0 !important;
  }

  /* Override root container */
  html #root,
  #root {
    min-height: calc(100vh - 60px) !important;
    padding-top: 0 !important;
    margin-top: 0 !important;
  }

  /* Override main content container */
  html body .main-content,
  body .main-content,
  .main-content {
    padding: 4px !important;
    /* Minimal padding */
    margin: 0 !important;
    min-height: calc(100vh - 60px) !important;
  }

  /* Override Semi-UI layout */
  html body .semi-layout,
  body .semi-layout,
  .semi-layout,
  html body .logs-layout,
  body .logs-layout,
  .logs-layout {
    margin: 0 !important;
    padding: 0 !important;
    /* Remove all padding */
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
  }

  /* ==============================|| NAVIGATION MENU FIXES ||============================== */

  /* Fix navigation menu positioning only - NOT content headers */
  html body .semi-navigation,
  body .semi-navigation,
  .semi-navigation {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 1000 !important;
    margin: 0 !important;
    border-radius: 0 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    background-color: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
  }

  /* Ensure content headers are NOT sticky */
  html body .semi-layout-header,
  body .semi-layout-header,
  .semi-layout-header,
  html body .logs-header,
  body .logs-header,
  .logs-header {
    position: static !important;
    background: transparent !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    box-shadow: none !important;
    z-index: auto !important;
  }

  html body .semi-layout-header h3,
  body .semi-layout-header h3,
  .semi-layout-header h3,
  html body .logs-header h3,
  body .logs-header h3,
  .logs-header h3 {
    font-size: 18px !important;
    font-weight: 600 !important;
    margin: 0 !important;
    line-height: 1.3 !important;
    color: #212529 !important;
  }

  /* ==============================|| FORM OPTIMIZATION ||============================== */

  /* Form container */
  html body .semi-form,
  body .semi-form,
  .semi-form,
  html body .logs-form,
  body .logs-form,
  .logs-form {
    padding: 8px !important;
    /* Reduced padding */
    margin: 4px 0 !important;
    /* Minimal margin */
    background-color: var(--semi-color-bg-0) !important;
    border: none !important;
    /* Remove borders */
  }

  /* Form fields */
  html body .semi-form-field,
  body .semi-form-field,
  .semi-form-field {
    width: 100% !important;
    margin-bottom: 16px !important;
  }

  /* Form inputs and controls */
  html body .semi-input,
  html body .semi-datepicker,
  html body .semi-select,
  html body .semi-autocomplete,
  html body .semi-button,
  body .semi-input,
  body .semi-datepicker,
  body .semi-select,
  body .semi-autocomplete,
  body .semi-button,
  .semi-input,
  .semi-datepicker,
  .semi-select,
  .semi-autocomplete,
  .semi-button,
  html body .logs-select,
  body .logs-select,
  .logs-select {
    width: 100% !important;
    min-height: 44px !important;
  }

  html body .semi-input-wrapper,
  body .semi-input-wrapper,
  .semi-input-wrapper {
    width: 100% !important;
  }

  html body .semi-input,
  body .semi-input,
  .semi-input {
    padding: 12px 16px !important;
    font-size: 16px !important;
    border-radius: 8px !important;
    border: 1px solid #ced4da !important;
    background-color: #ffffff !important;
  }

  /* ==============================|| TABLE COMPLETE OVERRIDE ||============================== */

  /* Hide table headers - MAXIMUM SPECIFICITY */
  html body .semi-table-thead,
  body .semi-table-thead,
  .semi-table-thead {
    display: none !important;
  }

  /* Table container reset */
  html body .semi-table,
  body .semi-table,
  .semi-table,
  html body .logs-table,
  body .logs-table,
  .logs-table {
    border: none !important;
    box-shadow: none !important;
    margin: 0 !important;
    background: transparent !important;
  }

  /* Card-based row layout - MAXIMUM SPECIFICITY */
  html body .semi-table-tbody .semi-table-row,
  body .semi-table-tbody .semi-table-row,
  .semi-table-tbody .semi-table-row {
    display: block !important;
    background-color: var(--semi-color-bg-1) !important;
    border: none !important;
    /* Remove all borders */
    border-radius: 4px !important;
    margin: 2px 0 !important;
    /* Minimal vertical margin only */
    padding: 8px !important;
    /* Reduced padding */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
    transition: transform 0.2s ease, box-shadow 0.2s ease !important;
    width: 100% !important;
    /* Full width */
  }

  html body .semi-table-tbody .semi-table-row:hover,
  body .semi-table-tbody .semi-table-row:hover,
  .semi-table-tbody .semi-table-row:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
  }

  /* Cell styling - MAXIMUM SPECIFICITY */
  html body .semi-table-tbody .semi-table-row-cell,
  body .semi-table-tbody .semi-table-row-cell,
  .semi-table-tbody .semi-table-row-cell {
    display: flex !important;
    align-items: flex-start !important;
    padding: 8px 0 !important;
    border: none !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    line-height: 1.5 !important;
    min-height: auto !important;
  }

  /* Data labels using CSS content */
  html body .semi-table-tbody .semi-table-row-cell:before,
  body .semi-table-tbody .semi-table-row-cell:before,
  .semi-table-tbody .semi-table-row-cell:before {
    font-weight: 600 !important;
    color: #6c757d !important;
    font-size: 14px !important;
    min-width: 80px !important;
    margin-right: 12px !important;
    flex-shrink: 0 !important;
  }

  html body .semi-table-tbody .semi-table-row-cell>*,
  body .semi-table-tbody .semi-table-row-cell>*,
  .semi-table-tbody .semi-table-row-cell>* {
    flex: 1 !important;
    margin: 0 !important;
  }

  /* Add data labels for mobile */
  html body .semi-table-tbody .semi-table-row-cell:nth-child(1):before,
  body .semi-table-tbody .semi-table-row-cell:nth-child(1):before,
  .semi-table-tbody .semi-table-row-cell:nth-child(1):before {
    content: '时间: ' !important;
  }

  html body .semi-table-tbody .semi-table-row-cell:nth-child(2):before,
  body .semi-table-tbody .semi-table-row-cell:nth-child(2):before,
  .semi-table-tbody .semi-table-row-cell:nth-child(2):before {
    content: '渠道: ' !important;
  }

  html body .semi-table-tbody .semi-table-row-cell:nth-child(3):before,
  body .semi-table-tbody .semi-table-row-cell:nth-child(3):before,
  .semi-table-tbody .semi-table-row-cell:nth-child(3):before {
    content: '用户: ' !important;
  }

  html body .semi-table-tbody .semi-table-row-cell:nth-child(4):before,
  body .semi-table-tbody .semi-table-row-cell:nth-child(4):before,
  .semi-table-tbody .semi-table-row-cell:nth-child(4):before {
    content: '令牌: ' !important;
  }

  html body .semi-table-tbody .semi-table-row-cell:nth-child(5):before,
  body .semi-table-tbody .semi-table-row-cell:nth-child(5):before,
  .semi-table-tbody .semi-table-row-cell:nth-child(5):before {
    content: '类型: ' !important;
  }

  html body .semi-table-tbody .semi-table-row-cell:nth-child(6):before,
  body .semi-table-tbody .semi-table-row-cell:nth-child(6):before,
  .semi-table-tbody .semi-table-row-cell:nth-child(6):before {
    content: '模型: ' !important;
  }

  html body .semi-table-tbody .semi-table-row-cell:nth-child(7):before,
  body .semi-table-tbody .semi-table-row-cell:nth-child(7):before,
  .semi-table-tbody .semi-table-row-cell:nth-child(7):before {
    content: '提示: ' !important;
  }

  html body .semi-table-tbody .semi-table-row-cell:nth-child(8):before,
  body .semi-table-tbody .semi-table-row-cell:nth-child(8):before,
  .semi-table-tbody .semi-table-row-cell:nth-child(8):before {
    content: '完成: ' !important;
  }

  html body .semi-table-tbody .semi-table-row-cell:nth-child(9):before,
  body .semi-table-tbody .semi-table-row-cell:nth-child(9):before,
  .semi-table-tbody .semi-table-row-cell:nth-child(9):before {
    content: '配额: ' !important;
  }

  html body .semi-table-tbody .semi-table-row-cell:nth-child(10):before,
  body .semi-table-tbody .semi-table-row-cell:nth-child(10):before,
  .semi-table-tbody .semi-table-row-cell:nth-child(10):before {
    content: '详情: ' !important;
  }

  /* ==============================|| COMPLETE BORDER REMOVAL ||============================== */

  /* Remove ALL borders with maximum specificity */
  html body .semi-table,
  html body .semi-table *,
  body .semi-table,
  body .semi-table *,
  .semi-table,
  .semi-table *,
  html body .logs-table,
  html body .logs-table *,
  body .logs-table,
  body .logs-table *,
  .logs-table,
  .logs-table * {
    border: none !important;
    border-top: none !important;
    border-bottom: none !important;
    border-left: none !important;
    border-right: none !important;
    border-collapse: separate !important;
  }

  /* ==============================|| MOBILE MENU STYLING ||============================== */

  /* Semi Design mobile navigation styling */
  html body .semi-navigation,
  body .semi-navigation,
  .semi-navigation {
    background-color: var(--semi-color-bg-0, #ffffff) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  }

  /* Semi Design mobile dropdown menu styling */
  html body .semi-dropdown-menu,
  body .semi-dropdown-menu,
  .semi-dropdown-menu {
    background-color: var(--semi-color-bg-0, #ffffff) !important;
    border: 1px solid var(--semi-color-border, #e0e0e0) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border-radius: 6px !important;
  }

  /* Semi Design mobile dropdown items */
  html body .semi-dropdown-item,
  body .semi-dropdown-item,
  .semi-dropdown-item {
    background-color: var(--semi-color-bg-0, #ffffff) !important;
    color: var(--semi-color-text-0, #000000) !important;
    padding: 12px 16px !important;
    border-bottom: 1px solid var(--semi-color-border, #e0e0e0) !important;
    transition: all 0.2s ease !important;
  }

  html body .semi-dropdown-item:hover,
  body .semi-dropdown-item:hover,
  .semi-dropdown-item:hover {
    background-color: var(--semi-color-fill-0, #f5f5f5) !important;
  }

  html body .semi-dropdown-item:last-child,
  body .semi-dropdown-item:last-child,
  .semi-dropdown-item:last-child {
    border-bottom: none !important;
  }

  /* Dark theme support for Semi Design mobile menu */
  [theme-mode="dark"] html body .semi-navigation,
  [theme-mode="dark"] body .semi-navigation,
  [theme-mode="dark"] .semi-navigation {
    background-color: var(--semi-color-bg-0, #1e1e1e) !important;
  }

  [theme-mode="dark"] html body .semi-dropdown-menu,
  [theme-mode="dark"] body .semi-dropdown-menu,
  [theme-mode="dark"] .semi-dropdown-menu {
    background-color: var(--semi-color-bg-0, #1e1e1e) !important;
    border: 1px solid var(--semi-color-border, #333333) !important;
  }

  [theme-mode="dark"] html body .semi-dropdown-item,
  [theme-mode="dark"] body .semi-dropdown-item,
  [theme-mode="dark"] .semi-dropdown-item {
    background-color: var(--semi-color-bg-0, #1e1e1e) !important;
    color: var(--semi-color-text-0, #ffffff) !important;
    border-bottom: 1px solid var(--semi-color-border, #333333) !important;
  }

  [theme-mode="dark"] html body .semi-dropdown-item:hover,
  [theme-mode="dark"] body .semi-dropdown-item:hover,
  [theme-mode="dark"] .semi-dropdown-item:hover {
    background-color: var(--semi-color-fill-0, #333333) !important;
  }

  /* ==============================|| LOG TAGS DARK MODE FIX ||============================== */

  /* Fix bright colored tags in logs for better dark mode readability */
  [theme-mode="dark"] html body .semi-tag,
  [theme-mode="dark"] body .semi-tag,
  [theme-mode="dark"] .semi-tag {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: var(--semi-color-text-0, #ffffff) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
  }

  /* Specific color adjustments for different tag colors in dark mode */
  [theme-mode="dark"] html body .semi-tag-cyan,
  [theme-mode="dark"] body .semi-tag-cyan,
  [theme-mode="dark"] .semi-tag-cyan {
    background-color: rgba(6, 182, 212, 0.2) !important;
    color: #67e8f9 !important;
    border: 1px solid rgba(6, 182, 212, 0.3) !important;
  }

  [theme-mode="dark"] html body .semi-tag-lime,
  [theme-mode="dark"] body .semi-tag-lime,
  [theme-mode="dark"] .semi-tag-lime {
    background-color: rgba(132, 204, 22, 0.2) !important;
    color: #a3e635 !important;
    border: 1px solid rgba(132, 204, 22, 0.3) !important;
  }

  [theme-mode="dark"] html body .semi-tag-orange,
  [theme-mode="dark"] body .semi-tag-orange,
  [theme-mode="dark"] .semi-tag-orange {
    background-color: rgba(249, 115, 22, 0.2) !important;
    color: #fb923c !important;
    border: 1px solid rgba(249, 115, 22, 0.3) !important;
  }

  [theme-mode="dark"] html body .semi-tag-purple,
  [theme-mode="dark"] body .semi-tag-purple,
  [theme-mode="dark"] .semi-tag-purple {
    background-color: rgba(147, 51, 234, 0.2) !important;
    color: #c084fc !important;
    border: 1px solid rgba(147, 51, 234, 0.3) !important;
  }

  [theme-mode="dark"] html body .semi-tag-violet,
  [theme-mode="dark"] body .semi-tag-violet,
  [theme-mode="dark"] .semi-tag-violet {
    background-color: rgba(139, 69, 195, 0.2) !important;
    color: #a855f7 !important;
    border: 1px solid rgba(139, 69, 195, 0.3) !important;
  }

  [theme-mode="dark"] html body .semi-tag-blue,
  [theme-mode="dark"] body .semi-tag-blue,
  [theme-mode="dark"] .semi-tag-blue {
    background-color: rgba(59, 130, 246, 0.2) !important;
    color: #60a5fa !important;
    border: 1px solid rgba(59, 130, 246, 0.3) !important;
  }

  [theme-mode="dark"] html body .semi-tag-yellow,
  [theme-mode="dark"] body .semi-tag-yellow,
  [theme-mode="dark"] .semi-tag-yellow {
    background-color: rgba(234, 179, 8, 0.2) !important;
    color: #fbbf24 !important;
    border: 1px solid rgba(234, 179, 8, 0.3) !important;
  }

  [theme-mode="dark"] html body .semi-tag-black,
  [theme-mode="dark"] body .semi-tag-black,
  [theme-mode="dark"] .semi-tag-black {
    background-color: rgba(128, 128, 128, 0.2) !important;
    color: #9ca3af !important;
    border: 1px solid rgba(128, 128, 128, 0.3) !important;
  }
}
