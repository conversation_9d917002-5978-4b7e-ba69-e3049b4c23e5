import React from 'react';
import { styled } from '@mui/material/styles';
import TablePagination from '@mui/material/TablePagination';
import Paper from '@mui/material/Paper';

const StyledPaginationContainer = styled(Paper)(({ theme }) => ({
  position: 'fixed',
  bottom: '20px',
  left: '50%',
  transform: 'translateX(-50%)',
  zIndex: 1000,
  backgroundColor: theme.palette.background.paper,
  borderRadius: '8px',
  padding: '8px 16px',
  boxShadow: theme.shadows[8],
  backdropFilter: 'blur(10px)',
  border: `1px solid ${theme.palette.divider}`,
  
  [theme.breakpoints.down('md')]: {
    bottom: '10px',
    left: '10px',
    right: '10px',
    transform: 'none',
    width: 'auto',
    maxWidth: 'calc(100vw - 20px)',
    padding: '4px 8px',
  },
  
  '& .MuiTablePagination-root': {
    overflow: 'visible',
  },
  
  '& .MuiTablePagination-toolbar': {
    minHeight: '40px',
    paddingLeft: '8px',
    paddingRight: '8px',
    
    [theme.breakpoints.down('md')]: {
      minHeight: '36px',
      paddingLeft: '4px',
      paddingRight: '4px',
    },
  },
  
  '& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows': {
    fontSize: '0.875rem',
    
    [theme.breakpoints.down('md')]: {
      fontSize: '0.75rem',
    },
  },
  
  '& .MuiTablePagination-actions': {
    marginLeft: '8px',
    
    [theme.breakpoints.down('md')]: {
      marginLeft: '4px',
    },
  },
  
  '& .MuiIconButton-root': {
    padding: '4px',
    
    [theme.breakpoints.down('md')]: {
      padding: '2px',
    },
  },
}));

const FixedPagination = ({
  page,
  count,
  rowsPerPage,
  onPageChange,
  rowsPerPageOptions = [10],
  ...props
}) => {
  // Don't render if there's only one page or no data
  if (!count || count <= rowsPerPage) {
    return null;
  }

  return (
    <StyledPaginationContainer elevation={3}>
      <TablePagination
        page={page}
        component="div"
        count={count}
        rowsPerPage={rowsPerPage}
        onPageChange={onPageChange}
        rowsPerPageOptions={rowsPerPageOptions}
        {...props}
      />
    </StyledPaginationContainer>
  );
};

export default FixedPagination;
