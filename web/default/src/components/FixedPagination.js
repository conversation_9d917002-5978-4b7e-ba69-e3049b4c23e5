import React, { useState, useEffect } from 'react';
import { Pagination, Button } from 'semantic-ui-react';
import './FixedPagination.css';

const FixedPagination = ({
  activePage,
  onPageChange,
  totalPages,
  size = 'small',
  siblingRange = 1,
  ...props
}) => {
  const [isNarrowScreen, setIsNarrowScreen] = useState(false);

  useEffect(() => {
    const checkScreenWidth = () => {
      const isNarrow = window.innerWidth < 768;
      console.log(`[FixedPagination] Screen width: ${window.innerWidth}px, isNarrow: ${isNarrow}`);
      setIsNarrowScreen(isNarrow);
    };

    checkScreenWidth();
    window.addEventListener('resize', checkScreenWidth);
    return () => window.removeEventListener('resize', checkScreenWidth);
  }, []);

  useEffect(() => {
    console.log(`[FixedPagination] Component rendered - activePage: ${activePage}, totalPages: ${totalPages}, isNarrowScreen: ${isNarrowScreen}`);

    // Ensure pagination stays visible after each render
    const timer = setTimeout(() => {
      const paginationElement = document.querySelector('.pagination-container');
      if (paginationElement) {
        paginationElement.style.visibility = 'visible';
        paginationElement.style.opacity = '1';
        paginationElement.style.display = 'flex';
        console.log(`[FixedPagination] Forced pagination visibility after render`);
      }
    }, 50); // Small delay to ensure DOM is updated

    return () => clearTimeout(timer);
  }, [activePage, totalPages, isNarrowScreen]);

  if (!totalPages || totalPages <= 1) {
    console.log(`[FixedPagination] Hiding pagination - totalPages: ${totalPages}`);
    return null; // Hide pagination for single page
  }

  const handlePrevious = (e) => {
    e.preventDefault();
    e.stopPropagation();
    console.log(`[FixedPagination] Previous clicked - current page: ${activePage}`);

    // Force pagination to stay visible during state changes
    const paginationElement = document.querySelector('.pagination-container');
    if (paginationElement) {
      paginationElement.style.visibility = 'visible';
      paginationElement.style.opacity = '1';
      paginationElement.style.display = 'flex';
    }

    if (activePage > 1 && onPageChange) {
      onPageChange(null, { activePage: activePage - 1 });
    }
  };

  const handleNext = (e) => {
    e.preventDefault();
    e.stopPropagation();
    console.log(`[FixedPagination] Next clicked - current page: ${activePage}`);

    // Force pagination to stay visible during state changes
    const paginationElement = document.querySelector('.pagination-container');
    if (paginationElement) {
      paginationElement.style.visibility = 'visible';
      paginationElement.style.opacity = '1';
      paginationElement.style.display = 'flex';
    }

    if (activePage < totalPages && onPageChange) {
      onPageChange(null, { activePage: activePage + 1 });
    }
  };

  const handlePageChange = (e, data) => {
    e.preventDefault();
    e.stopPropagation();
    console.log(`[FixedPagination] Page change - from ${activePage} to ${data.activePage}`);

    // Force pagination to stay visible during state changes
    const paginationElement = document.querySelector('.pagination-container');
    if (paginationElement) {
      paginationElement.style.visibility = 'visible';
      paginationElement.style.opacity = '1';
      paginationElement.style.display = 'flex';
    }

    if (onPageChange) {
      onPageChange(e, data);
    }
  };

  if (isNarrowScreen) {
    // Show only prev/next buttons on narrow screens
    console.log(`[FixedPagination] Rendering narrow screen layout`);
    return (
      <div className="pagination-container pagination-narrow" style={{ position: 'fixed', bottom: '20px', left: '50%', transform: 'translateX(-50%)', zIndex: 1000 }}>
        <Button
          disabled={activePage <= 1}
          onClick={handlePrevious}
          size="mini"
          compact
          className="pagination-nav-btn pagination-prev-btn"
          aria-label="Previous page"
          style={{ minWidth: '40px', height: '40px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          <span style={{ fontSize: '16px', fontWeight: 'bold' }}>‹</span>
        </Button>
        <span className="pagination-info" style={{ padding: '0 12px', fontSize: '14px', fontWeight: '500', whiteSpace: 'nowrap' }}>
          Page {activePage} of {totalPages}
        </span>
        <Button
          disabled={activePage >= totalPages}
          onClick={handleNext}
          size="mini"
          compact
          className="pagination-nav-btn pagination-next-btn"
          aria-label="Next page"
          style={{ minWidth: '40px', height: '40px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}
        >
          <span style={{ fontSize: '16px', fontWeight: 'bold' }}>›</span>
        </Button>
      </div>
    );
  }

  // Show full pagination on wide screens
  console.log(`[FixedPagination] Rendering wide screen layout`);
  return (
    <div className="pagination-container pagination-wide">
      <Pagination
        activePage={activePage}
        onPageChange={handlePageChange}
        totalPages={totalPages}
        size={size}
        siblingRange={siblingRange}
        {...props}
      />
    </div>
  );
};

export default FixedPagination;
