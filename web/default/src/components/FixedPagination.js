import React, { useState, useEffect } from 'react';
import { Pagination, Button } from 'semantic-ui-react';
import './FixedPagination.css';

const FixedPagination = ({
  activePage,
  onPageChange,
  totalPages,
  size = 'small',
  siblingRange = 1,
  ...props
}) => {
  const [isNarrowScreen, setIsNarrowScreen] = useState(false);
  const [hoveredButton, setHoveredButton] = useState(null);

  useEffect(() => {
    const checkScreenWidth = () => {
      const isNarrow = window.innerWidth < 768;
      console.log(`[FixedPagination] Screen width: ${window.innerWidth}px, isNarrow: ${isNarrow}`);
      setIsNarrowScreen(isNarrow);
    };

    checkScreenWidth();
    window.addEventListener('resize', checkScreenWidth);
    return () => window.removeEventListener('resize', checkScreenWidth);
  }, []);

  useEffect(() => {
    console.log(`[FixedPagination] Component rendered - activePage: ${activePage}, totalPages: ${totalPages}, isNarrowScreen: ${isNarrowScreen}`);

    // Ensure pagination stays visible after each render
    const timer = setTimeout(() => {
      const paginationElement = document.querySelector('.pagination-container');
      if (paginationElement) {
        paginationElement.style.visibility = 'visible';
        paginationElement.style.opacity = '1';
        paginationElement.style.display = 'flex';
        console.log(`[FixedPagination] Forced pagination visibility after render`);
      }
    }, 50); // Small delay to ensure DOM is updated

    return () => clearTimeout(timer);
  }, [activePage, totalPages, isNarrowScreen]);

  if (!totalPages || totalPages <= 1) {
    console.log(`[FixedPagination] Hiding pagination - totalPages: ${totalPages}`);
    return null; // Hide pagination for single page
  }

  const handlePrevious = (e) => {
    e.preventDefault();
    e.stopPropagation();
    console.log(`[FixedPagination] Previous clicked - current page: ${activePage}`);

    // Force pagination to stay visible during state changes
    const paginationElement = document.querySelector('.pagination-container');
    if (paginationElement) {
      paginationElement.style.visibility = 'visible';
      paginationElement.style.opacity = '1';
      paginationElement.style.display = 'flex';
    }

    if (activePage > 1 && onPageChange) {
      onPageChange(null, { activePage: activePage - 1 });
    }
  };

  const handleNext = (e) => {
    e.preventDefault();
    e.stopPropagation();
    console.log(`[FixedPagination] Next clicked - current page: ${activePage}`);

    // Force pagination to stay visible during state changes
    const paginationElement = document.querySelector('.pagination-container');
    if (paginationElement) {
      paginationElement.style.visibility = 'visible';
      paginationElement.style.opacity = '1';
      paginationElement.style.display = 'flex';
    }

    if (activePage < totalPages && onPageChange) {
      onPageChange(null, { activePage: activePage + 1 });
    }
  };

  const handlePageChange = (e, data) => {
    e.preventDefault();
    e.stopPropagation();
    console.log(`[FixedPagination] Page change - from ${activePage} to ${data.activePage}`);

    // Force pagination to stay visible during state changes
    const paginationElement = document.querySelector('.pagination-container');
    if (paginationElement) {
      paginationElement.style.visibility = 'visible';
      paginationElement.style.opacity = '1';
      paginationElement.style.display = 'flex';
    }

    if (onPageChange) {
      onPageChange(e, data);
    }
  };

  if (isNarrowScreen) {
    // Show only prev/next buttons on narrow screens
    console.log(`[FixedPagination] Rendering narrow screen layout`);
    return (
      <div
        className="pagination-container pagination-narrow"
        style={{
          position: 'fixed',
          bottom: '20px',
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          background: 'rgba(255, 255, 255, 0.95)',
          borderRadius: '8px',
          padding: '8px 12px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
          border: '1px solid rgba(0, 0, 0, 0.1)',
          maxWidth: '90vw',
          visibility: 'visible',
          opacity: 1
        }}
      >
        <Button
          disabled={activePage <= 1}
          onClick={handlePrevious}
          onMouseEnter={() => setHoveredButton('prev')}
          onMouseLeave={() => setHoveredButton(null)}
          size="mini"
          compact
          className="pagination-nav-btn pagination-prev-btn"
          aria-label="Previous page"
          style={{
            minWidth: '36px',
            height: '36px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: activePage <= 1
              ? 'rgba(0, 0, 0, 0.05)'
              : hoveredButton === 'prev'
                ? 'rgba(24, 144, 255, 0.2)'
                : 'rgba(24, 144, 255, 0.1)',
            border: activePage <= 1
              ? '1px solid rgba(0, 0, 0, 0.1)'
              : hoveredButton === 'prev'
                ? '1px solid rgba(24, 144, 255, 0.5)'
                : '1px solid rgba(24, 144, 255, 0.3)',
            borderRadius: '6px',
            color: activePage <= 1 ? 'rgba(0, 0, 0, 0.3)' : '#1890ff',
            cursor: activePage <= 1 ? 'not-allowed' : 'pointer',
            transform: hoveredButton === 'prev' && activePage > 1 ? 'translateY(-1px)' : 'translateY(0)',
            transition: 'all 0.2s ease'
          }}
        >
          <span style={{ fontSize: '16px', fontWeight: 'bold' }}>‹</span>
        </Button>
        <span
          className="pagination-info"
          style={{
            padding: '0 8px',
            fontSize: '13px',
            fontWeight: '500',
            whiteSpace: 'nowrap',
            color: '#333',
            minWidth: 'fit-content'
          }}
        >
          {activePage} / {totalPages}
        </span>
        <Button
          disabled={activePage >= totalPages}
          onClick={handleNext}
          onMouseEnter={() => setHoveredButton('next')}
          onMouseLeave={() => setHoveredButton(null)}
          size="mini"
          compact
          className="pagination-nav-btn pagination-next-btn"
          aria-label="Next page"
          style={{
            minWidth: '36px',
            height: '36px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            background: activePage >= totalPages
              ? 'rgba(0, 0, 0, 0.05)'
              : hoveredButton === 'next'
                ? 'rgba(24, 144, 255, 0.2)'
                : 'rgba(24, 144, 255, 0.1)',
            border: activePage >= totalPages
              ? '1px solid rgba(0, 0, 0, 0.1)'
              : hoveredButton === 'next'
                ? '1px solid rgba(24, 144, 255, 0.5)'
                : '1px solid rgba(24, 144, 255, 0.3)',
            borderRadius: '6px',
            color: activePage >= totalPages ? 'rgba(0, 0, 0, 0.3)' : '#1890ff',
            cursor: activePage >= totalPages ? 'not-allowed' : 'pointer',
            transform: hoveredButton === 'next' && activePage < totalPages ? 'translateY(-1px)' : 'translateY(0)',
            transition: 'all 0.2s ease'
          }}
        >
          <span style={{ fontSize: '16px', fontWeight: 'bold' }}>›</span>
        </Button>
      </div>
    );
  }

  // Show full pagination on wide screens
  console.log(`[FixedPagination] Rendering wide screen layout`);
  return (
    <div className="pagination-container pagination-wide">
      <Pagination
        activePage={activePage}
        onPageChange={handlePageChange}
        totalPages={totalPages}
        size={size}
        siblingRange={siblingRange}
        {...props}
      />
    </div>
  );
};

export default FixedPagination;
