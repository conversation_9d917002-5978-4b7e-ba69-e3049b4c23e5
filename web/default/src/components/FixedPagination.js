import React from 'react';

const FixedPagination = ({ activePage, totalPages, onPageChange }) => {
  // Don't render pagination if there's only one page or no pages
  if (!totalPages || totalPages <= 1) {
    return null;
  }

  const handlePrevious = () => {
    if (activePage > 1 && onPageChange) {
      onPageChange(null, { activePage: activePage - 1 });
    }
  };

  const handleNext = () => {
    if (activePage < totalPages && onPageChange) {
      onPageChange(null, { activePage: activePage + 1 });
    }
  };

  // Styles for the pagination container
  const paginationStyle = {
    position: 'fixed',
    bottom: '20px',
    left: '50%',
    transform: 'translateX(-50%)',
    zIndex: 1000,
    display: 'flex',
    alignItems: 'center',
    gap: '12px',
    background: 'white',
    borderRadius: '8px',
    padding: '12px 16px',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
    border: '1px solid #e0e0e0',
    fontFamily: 'system-ui, -apple-system, sans-serif',
    maxWidth: '90vw'
  };

  const buttonStyle = {
    width: '40px',
    height: '40px',
    border: '1px solid #d0d0d0',
    borderRadius: '6px',
    background: 'white',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '18px',
    fontWeight: 'bold',
    color: '#333',
    transition: 'all 0.2s ease',
    userSelect: 'none'
  };

  const disabledButtonStyle = {
    ...buttonStyle,
    background: '#f5f5f5',
    color: '#ccc',
    cursor: 'not-allowed',
    border: '1px solid #e0e0e0'
  };

  const pageInfoStyle = {
    fontSize: '14px',
    fontWeight: '500',
    color: '#666',
    minWidth: '60px',
    textAlign: 'center'
  };

  return (
    <div style={paginationStyle}>
      <button
        style={activePage <= 1 ? disabledButtonStyle : buttonStyle}
        onClick={handlePrevious}
        disabled={activePage <= 1}
        onMouseEnter={(e) => {
          if (activePage > 1) {
            e.target.style.background = '#f0f8ff';
            e.target.style.borderColor = '#1890ff';
            e.target.style.color = '#1890ff';
          }
        }}
        onMouseLeave={(e) => {
          if (activePage > 1) {
            e.target.style.background = 'white';
            e.target.style.borderColor = '#d0d0d0';
            e.target.style.color = '#333';
          }
        }}
      >
        ‹
      </button>

      <span style={pageInfoStyle}>
        {activePage} / {totalPages}
      </span>

      <button
        style={activePage >= totalPages ? disabledButtonStyle : buttonStyle}
        onClick={handleNext}
        disabled={activePage >= totalPages}
        onMouseEnter={(e) => {
          if (activePage < totalPages) {
            e.target.style.background = '#f0f8ff';
            e.target.style.borderColor = '#1890ff';
            e.target.style.color = '#1890ff';
          }
        }}
        onMouseLeave={(e) => {
          if (activePage < totalPages) {
            e.target.style.background = 'white';
            e.target.style.borderColor = '#d0d0d0';
            e.target.style.color = '#333';
          }
        }}
      >
        ›
      </button>
    </div>
  );
};

export default FixedPagination;
