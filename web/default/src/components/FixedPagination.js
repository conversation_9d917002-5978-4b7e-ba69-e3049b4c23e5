import React, { useState, useEffect } from 'react';
import { Pagination, Button } from 'semantic-ui-react';
import './FixedPagination.css';

const FixedPagination = ({
  activePage,
  onPageChange,
  totalPages,
  size = 'small',
  siblingRange = 1,
  ...props
}) => {
  const [isNarrowScreen, setIsNarrowScreen] = useState(false);

  useEffect(() => {
    const checkScreenWidth = () => {
      setIsNarrowScreen(window.innerWidth < 768); // Mobile breakpoint
    };

    checkScreenWidth();
    window.addEventListener('resize', checkScreenWidth);
    return () => window.removeEventListener('resize', checkScreenWidth);
  }, []);

  if (!totalPages || totalPages <= 1) {
    return null; // Hide pagination for single page
  }

  const handlePrevious = () => {
    if (activePage > 1 && onPageChange) {
      onPageChange(null, { activePage: activePage - 1 });
    }
  };

  const handleNext = () => {
    if (activePage < totalPages && onPageChange) {
      onPageChange(null, { activePage: activePage + 1 });
    }
  };

  if (isNarrowScreen) {
    // Show only prev/next buttons on narrow screens
    return (
      <div className="pagination-container pagination-narrow">
        <Button
          icon="chevron left"
          disabled={activePage <= 1}
          onClick={handlePrevious}
          size="mini"
          compact
          className="pagination-nav-btn"
        />
        <span className="pagination-info">
          Page {activePage} of {totalPages}
        </span>
        <Button
          icon="chevron right"
          disabled={activePage >= totalPages}
          onClick={handleNext}
          size="mini"
          compact
          className="pagination-nav-btn"
        />
      </div>
    );
  }

  // Show full pagination on wide screens
  return (
    <div className="pagination-container pagination-wide">
      <Pagination
        activePage={activePage}
        onPageChange={onPageChange}
        totalPages={totalPages}
        size={size}
        siblingRange={siblingRange}
        {...props}
      />
    </div>
  );
};

export default FixedPagination;
