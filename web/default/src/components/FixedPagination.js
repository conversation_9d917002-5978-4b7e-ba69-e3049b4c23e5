import React, { useState, useEffect } from 'react';
import { Pagination, Button } from 'semantic-ui-react';
import './FixedPagination.css';

const FixedPagination = ({
  activePage,
  onPageChange,
  totalPages,
  size = 'small',
  siblingRange = 1,
  ...props
}) => {
  const [isNarrowScreen, setIsNarrowScreen] = useState(false);

  useEffect(() => {
    const checkScreenWidth = () => {
      const isNarrow = window.innerWidth < 768;
      console.log(`[FixedPagination] Screen width: ${window.innerWidth}px, isNarrow: ${isNarrow}`);
      setIsNarrowScreen(isNarrow);
    };

    checkScreenWidth();
    window.addEventListener('resize', checkScreenWidth);
    return () => window.removeEventListener('resize', checkScreenWidth);
  }, []);

  useEffect(() => {
    console.log(`[FixedPagination] Component rendered - activePage: ${activePage}, totalPages: ${totalPages}, isNarrowScreen: ${isNarrowScreen}`);
  }, [activePage, totalPages, isNarrowScreen]);

  if (!totalPages || totalPages <= 1) {
    console.log(`[FixedPagination] Hiding pagination - totalPages: ${totalPages}`);
    return null; // Hide pagination for single page
  }

  const handlePrevious = (e) => {
    e.preventDefault();
    e.stopPropagation();
    console.log(`[FixedPagination] Previous clicked - current page: ${activePage}`);
    if (activePage > 1 && onPageChange) {
      onPageChange(null, { activePage: activePage - 1 });
    }
  };

  const handleNext = (e) => {
    e.preventDefault();
    e.stopPropagation();
    console.log(`[FixedPagination] Next clicked - current page: ${activePage}`);
    if (activePage < totalPages && onPageChange) {
      onPageChange(null, { activePage: activePage + 1 });
    }
  };

  const handlePageChange = (e, data) => {
    e.preventDefault();
    e.stopPropagation();
    console.log(`[FixedPagination] Page change - from ${activePage} to ${data.activePage}`);
    if (onPageChange) {
      onPageChange(e, data);
    }
  };

  if (isNarrowScreen) {
    // Show only prev/next buttons on narrow screens
    console.log(`[FixedPagination] Rendering narrow screen layout`);
    return (
      <div className="pagination-container pagination-narrow">
        <Button
          icon="chevron left"
          disabled={activePage <= 1}
          onClick={handlePrevious}
          size="mini"
          compact
          className="pagination-nav-btn"
          aria-label="Previous page"
        />
        <span className="pagination-info">
          Page {activePage} of {totalPages}
        </span>
        <Button
          icon="chevron right"
          disabled={activePage >= totalPages}
          onClick={handleNext}
          size="mini"
          compact
          className="pagination-nav-btn"
          aria-label="Next page"
        />
      </div>
    );
  }

  // Show full pagination on wide screens
  console.log(`[FixedPagination] Rendering wide screen layout`);
  return (
    <div className="pagination-container pagination-wide">
      <Pagination
        activePage={activePage}
        onPageChange={handlePageChange}
        totalPages={totalPages}
        size={size}
        siblingRange={siblingRange}
        {...props}
      />
    </div>
  );
};

export default FixedPagination;
