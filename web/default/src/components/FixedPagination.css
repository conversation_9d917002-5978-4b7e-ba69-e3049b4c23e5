.pagination-container {
  position: fixed !important;
  bottom: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 1000 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  border-radius: 8px !important;
  padding: 12px 16px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  /* Prevent page jumping */
  pointer-events: auto !important;
  visibility: visible !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Wide screens: Show all page numbers */
@media (min-width: 768px) {
  .pagination-container .ui.pagination.menu {
    /* Let Semantic UI handle normal pagination display */
  }
}

/* Narrow screens: Show only prev/next buttons */
@media (max-width: 767px) {
  .pagination-container {
    bottom: 10px !important;
    left: 10px !important;
    right: 10px !important;
    transform: none !important;
    width: auto !important;
    max-width: calc(100vw - 20px) !important;
    padding: 8px 12px !important;
    /* Ensure visibility on narrow screens */
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  /* Hide page numbers on mobile, keep only prev/next */
  .pagination-container .ui.pagination.menu .item:not(.icon) {
    display: none !important;
  }

  /* Show only prev/next arrow buttons */
  .pagination-container .ui.pagination.menu .item.icon {
    display: flex !important;
    min-width: 40px !important;
    height: 40px !important;
    margin: 0 4px !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

/* Narrow screen pagination layout - simplified to avoid conflicts */
.pagination-narrow {
  /* Let inline styles handle positioning to avoid conflicts */
}

.pagination-narrow .pagination-info {
  font-size: 12px !important;
  font-weight: 500 !important;
  color: #333 !important;
  white-space: nowrap !important;
  flex-shrink: 0 !important;
  min-width: 0 !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.pagination-nav-btn {
  /* Let inline styles handle all styling to avoid conflicts */
  flex-shrink: 0 !important;
}

.pagination-nav-btn:hover:not(.disabled) {
  /* Hover effects handled by inline styles */
}

.pagination-prev-btn span,
.pagination-next-btn span {
  /* Font styling handled by inline styles */
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .pagination-container {
    background: rgba(45, 45, 45, 0.95);
    border-color: #444;
    color: white;
  }

  .pagination-narrow .pagination-info {
    color: #ccc;
  }
}

/* Additional responsive adjustments for narrow layout */
@media (max-width: 767px) {
  .pagination-container {
    padding: 6px 10px;
  }

  .pagination-narrow {
    gap: 6px;
  }

  .pagination-narrow .pagination-info {
    font-size: 11px;
  }

  .pagination-nav-btn {
    min-width: 28px !important;
    height: 28px !important;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .pagination-container {
    padding: 4px 8px !important;
    left: 5px !important;
    right: 5px !important;
  }

  .pagination-narrow {
    gap: 4px !important;
  }

  .pagination-narrow .pagination-info {
    font-size: 10px !important;
  }

  .pagination-nav-btn {
    min-width: 24px !important;
    height: 24px !important;
  }
}

/* Prevent page jumping by ensuring pagination doesn't affect document flow */
body {
  scroll-behavior: smooth;
}

/* Ensure pagination buttons don't cause scroll jumps */
.pagination-container * {
  scroll-behavior: auto !important;
}

.pagination-container button,
.pagination-container .ui.button {
  scroll-behavior: auto !important;
}

/* Additional fixes for Semantic UI pagination */
.pagination-container .ui.pagination.menu {
  margin: 0 !important;
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

.pagination-container .ui.pagination.menu .item {
  border: none !important;
  background: rgba(0, 0, 0, 0.05) !important;
  margin: 0 2px !important;
  border-radius: 4px !important;
}

.pagination-container .ui.pagination.menu .item:hover {
  background: rgba(0, 0, 0, 0.1) !important;
}

.pagination-container .ui.pagination.menu .item.active {
  background: #1890ff !important;
  color: white !important;
}

/* Simplified overrides to prevent conflicts */
.pagination-container {
  /* Let inline styles handle positioning */
}
