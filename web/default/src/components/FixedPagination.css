.pagination-container {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  padding: 12px 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Wide screens: Show all page numbers */
@media (min-width: 768px) {
  .pagination-container .ui.pagination.menu {
    /* Let Semantic UI handle normal pagination display */
  }
}

/* Narrow screens: Show only prev/next buttons */
@media (max-width: 767px) {
  .pagination-container {
    bottom: 10px;
    left: 10px;
    right: 10px;
    transform: none;
    width: auto;
    max-width: calc(100vw - 20px);
    padding: 8px 12px;
  }

  /* Hide page numbers on mobile, keep only prev/next */
  .pagination-container .ui.pagination.menu .item:not(.icon) {
    display: none !important;
  }

  /* Show only prev/next arrow buttons */
  .pagination-container .ui.pagination.menu .item.icon {
    display: flex !important;
    min-width: 40px;
    height: 40px;
    margin: 0 4px;
  }
}

/* Narrow screen pagination layout */
.pagination-narrow {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
  max-width: 100%;
  overflow: hidden;
}

.pagination-narrow .pagination-info {
  font-size: 12px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  flex-shrink: 0;
  min-width: 0;
}

.pagination-nav-btn {
  min-width: 32px !important;
  height: 32px !important;
  padding: 0 !important;
  flex-shrink: 0;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .pagination-container {
    background: rgba(45, 45, 45, 0.95);
    border-color: #444;
    color: white;
  }

  .pagination-narrow .pagination-info {
    color: #ccc;
  }
}

/* Additional responsive adjustments for narrow layout */
@media (max-width: 767px) {
  .pagination-container {
    padding: 6px 10px;
  }

  .pagination-narrow {
    gap: 6px;
  }

  .pagination-narrow .pagination-info {
    font-size: 11px;
  }

  .pagination-nav-btn {
    min-width: 28px !important;
    height: 28px !important;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .pagination-container {
    padding: 4px 8px;
    left: 5px;
    right: 5px;
  }

  .pagination-narrow {
    gap: 4px;
  }

  .pagination-narrow .pagination-info {
    font-size: 10px;
  }

  .pagination-nav-btn {
    min-width: 24px !important;
    height: 24px !important;
  }
}
